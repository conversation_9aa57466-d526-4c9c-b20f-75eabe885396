import mimetypes
import os
from typing import Optional

from fastapi import APIRouter, HTTPException
from fastapi import Form
from fastapi import Response
from fastapi import UploadFile
from fastapi.responses import ORJSONResponse
from pydantic import BaseModel
from starlette.responses import StreamingResponse

from records.config.shared_config import SharedConfig
from records.context import context as ctx
from records.db import api as db_api, base, models
from records.db import client_upload_state
from records.db.exceptions import (
    OptimisticLockError,
    InvalidStateTransitionError,
    UploadNotFoundError,
    UploadNotInExpectedStateError
)
from records.services import file_processing, detections, clients
from records.utils import utils, json_utils


def get_router(prefix='/'):
    router = APIRouter(prefix=os.path.join(prefix, 'client_uploads'))

    router.add_api_route("", list_client_uploads, methods=['GET'], name='List client uploads')
    router.add_api_route("", upload_client_upload, methods=['POST'], name='Create client upload')
    router.add_api_route("/{upload_id}", get_client_upload, methods=['GET'], name='Get client upload')
    router.add_api_route("/{upload_id}/download", get_client_upload_download, methods=['GET'], name='Download client upload')
    # router.add_api_route("/{upload_id}", update_client_upload, methods=['PUT'], name='Update client upload')
    router.add_api_route("/{upload_id}/action", client_upload_action, methods=['PUT'], name='Do action on client upload')
    router.add_api_route("/{upload_id}", delete_client_upload, methods=['DELETE'], name='Delete client upload')

    return router


default_result = [{'output': 'Unknown error'}]


async def list_client_uploads(
    limit: int = 25,
    page: int = 1,
    order: str = 'id',
    desc: bool = True,
    q: str = None,
    applied: bool = False,
    # session: AsyncSession = Depends(get_session)
):
    async with base.session_context():
        client_uploads, count = await db_api.list_client_uploads(
            limit=limit, page=page, order=order, desc=desc, q=q, applied=applied
        )
        file_ids = [f.file_id for f in client_uploads]
        if file_ids:
            files = await db_api.list_client_files_all(file_ids)
            file_map = {f.id: f for f in files}
        else:
            file_map = {}

        if client_uploads:
            client_init_det_items = await detections.list_detection_items(client_uploads[0].client_init_detection_id)
        else:
            client_init_det_items = []
        detection_item_map = {d['id']: d for d in client_init_det_items}

    upload_dicts = [f.to_dict() for f in client_uploads]
    for upload_dict in upload_dicts:
        upload_dict['file'] = file_map.get(upload_dict['file_id']).to_dict() if upload_dict['file_id'] in file_map else None
        detection_item = detection_item_map.get(upload_dict['client_init_detection_item_id'], {})

        await process_client_upload_output(upload_dict, detection_item)
        # upload_dict['status'] = detection_item.get('status', 'PROCESSING')

    found_client_map = await clients.client_uploads_find_by_eins(upload_dicts)
    for upload_dict in upload_dicts:
        upload_dict['exists'] = found_client_map.get(utils.clean_ein(upload_dict.get('ein')))
        upload_dict['client'] = None
        upload_dict['client_output'] = None

    return ORJSONResponse({
        'items': upload_dicts, 'count': count, 'limit': limit, 'page': page
    })


async def get_client_upload(upload_id: int):
    async with base.session_context():
        upload_db = await db_api.get_client_upload_by_id(upload_id)
        file_db = await db_api.get_client_file_by_id(upload_db.file_id)
        try:
            detection_item = await detections.get_detection_item(
                upload_db.client_init_detection_id, upload_db.client_init_detection_item_id
            )
        except:
            # Not found ?
            detection_item = None

    upload_dict = upload_db.to_dict()

    # upload_dict['status'] = detection_item.get('status', 'PROCESSING')
    await process_client_upload_output(upload_dict, detection_item)
    upload_dict = (await clients.merge_client_uploads_with_clients([upload_dict]))[0]

    upload_dict['file'] = file_db.to_dict()
    # upload_dict['manager'] = manager.to_dict() if manager else None
    return ORJSONResponse(upload_dict)


async def process_client_upload_output(upload_dict: dict, detection_item: dict):
    if upload_dict['status'] == models.ClientUploadStatus.APPLIED:
        return upload_dict

    detection_item = detection_item or {}
    is_success = detection_item.get('status') == 'SUCCESS'
    is_error = detection_item.get('status') == 'ERROR' or not detection_item
    has_existing_output = upload_dict['output'] is not None
    has_existing_error = upload_dict['error_message'] is not None

    if is_success:
        if not has_existing_output:
            output = detection_item.get('results', default_result)[-1]['output']
            new_output = json_utils.extract_json(output)
            upload_dict['output'] = new_output
            await db_api.update_client_upload(
                models.ClientUpload(id=upload_dict['id']),
                {'output': new_output, 'status': models.ClientUploadStatus.REVIEW}
            )
        upload_dict['status'] = 'REVIEW'
    elif is_error:
        if not has_existing_error:
            upload_dict['error_message'] = (detection_item.get('results') or default_result)[-1]['output']
            await db_api.update_client_upload(
                models.ClientUpload(id=upload_dict['id']),
                {'error_message': upload_dict['error_message']}
            )
        upload_dict['status'] = 'ERROR'
    else:
        upload_dict['status'] = detection_item.get('status', 'PROCESSING')

    return upload_dict


async def get_client_upload_download(upload_id: int, inline: bool = False):
    async with base.session_context():
        upload_db = await db_api.get_client_upload_by_id(upload_id)

        file_db = await db_api.get_client_file_by_id(upload_db.file_id)
        file_path = detections.get_upload_file_path(file_db)

    read_file = await SharedConfig().file_manager.read_file(file_path)
    stream = await SharedConfig().file_manager.get_data_generator(read_file)
    file_name = file_db.name.encode('latin-1', 'replace').decode('latin-1')
    content_disposition = f'attachment; filename="{file_name}"' if not inline else f'inline; filename="{file_name}"'
    content_length = file_db.size
    return StreamingResponse(
        stream,
        media_type=upload_db.file_type,
        headers={
            'Content-Disposition': content_disposition,
            'Content-Length': content_length
        }
    )


async def upload_client_upload(
    file: UploadFile,
    description: Optional[str] = Form(None),
):
    async with base.session_context():
        file_data = file.file.read()
        file_name = file.filename
        manager = ctx.current_manager()

        file_dict = {
            'name': file_name,
            'size': len(file_data),
            'hash': utils.hash_sha256(file_data),
            'file_type': mimetypes.guess_type(file_name)[0],
            'manager_id': manager.id,
            'description': description,
            # 'date': base.now(),
        }

        # Check for duplicate
        existing_file = await db_api.get_client_file_by_hash(None, file_dict['hash'], notfoundok=True)
        if existing_file:
            raise HTTPException(409, f"File already exists with name {existing_file.name}")

        file_db = await db_api.create_client_file(file_dict)
        await SharedConfig().file_manager.save_file(detections.get_upload_file_path(file_db), file_data)

        upload_dict = {
            'file_id': file_db.id,
            'manager_id': manager.id,
            'status': models.ClientUploadStatus.PROCESSING,
        }

        upload_db = await db_api.create_client_upload(upload_dict)
        upload_db, detection_item = await file_processing.process_init_upload(
            upload_db=upload_db, file_db=file_db, file_data=file_data, file_hash=file_dict['hash']
        )

    upload_dict = upload_db.to_dict()
    upload_dict['file'] = file_db.to_dict()

    return ORJSONResponse(upload_dict)


async def delete_client_upload(upload_id: int):
    async with base.session_context():
        await detections.delete_client_upload(upload_id=upload_id)

    return Response(status_code=204)


class ClientUploadAction(BaseModel):
    action: str


async def client_upload_action(
    upload_id: int,
    action: ClientUploadAction,
):
    async with base.session_context():
        upload_db = await db_api.get_client_upload_by_id(upload_id)
        if action == 'accept':
            await delete_client_upload(upload_id)
        else:
            raise HTTPException(400, f"Unknown action {action}")

    return Response(status_code=204)
