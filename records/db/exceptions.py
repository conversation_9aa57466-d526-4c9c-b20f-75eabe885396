"""Database-related exceptions for client upload state management."""


class ClientUploadStateError(Exception):
    """Base exception for client upload state-related errors."""
    pass


class OptimisticLockError(ClientUploadStateError):
    """Raised when optimistic locking fails due to concurrent modification."""
    
    def __init__(self, upload_id: int, expected_version: int, actual_version: int):
        self.upload_id = upload_id
        self.expected_version = expected_version
        self.actual_version = actual_version
        super().__init__(
            f"Upload {upload_id} was modified concurrently. "
            f"Expected version {expected_version}, but found {actual_version}"
        )


class InvalidStateTransitionError(ClientUploadStateError):
    """Raised when attempting an invalid state transition."""
    
    def __init__(self, upload_id: int, from_status: str, to_status: str):
        self.upload_id = upload_id
        self.from_status = from_status
        self.to_status = to_status
        super().__init__(
            f"Invalid state transition for upload {upload_id}: "
            f"cannot transition from '{from_status}' to '{to_status}'"
        )


class UploadNotFoundError(ClientUploadStateError):
    """Raised when upload is not found during state transition."""
    
    def __init__(self, upload_id: int):
        self.upload_id = upload_id
        super().__init__(f"Upload {upload_id} not found")


class UploadNotInExpectedStateError(ClientUploadStateError):
    """Raised when upload is not in the expected state for transition."""
    
    def __init__(self, upload_id: int, expected_status: str, actual_status: str):
        self.upload_id = upload_id
        self.expected_status = expected_status
        self.actual_status = actual_status
        super().__init__(
            f"Upload {upload_id} is not in expected state '{expected_status}'. "
            f"Current state: '{actual_status}'"
        )
