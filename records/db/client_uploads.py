from sqlalchemy import delete, update, text
from sqlalchemy import exc
from sqlalchemy.future import select

from records.db import base
from records.db import models
from records.db import client_upload_state
from records.utils import utils


@base.session_aware()
async def create_client_upload(values, session=None):
    client_upload = models.ClientUpload(**values)

    try:
        session.add(client_upload)
    except exc.SQLAlchemyError as e:
        raise RuntimeError(
            "Duplicate entry for ClientInitUpload: %s" % e
        )

    return client_upload


@base.session_aware()
async def update_client_upload(client_upload: models.ClientUpload, new_values: dict, session=None):
    new_values['updated_at'] = base.now()
    update_q = update(models.ClientUpload).where(models.ClientUpload.id == client_upload.id)
    update_q = update_q.values(new_values)

    await session.execute(update_q)

    client_upload.update(new_values)

    return client_upload


@base.session_aware()
async def delete_client_upload(id: int, session=None):
    delete_q = delete(models.ClientUpload).where(models.ClientUpload.id == id)
    await session.execute(delete_q)


@base.session_aware()
async def delete_client_uploads(ids: list[int] = None, client_id: str = None, session=None):
    return await base.delete_client_datas(models.ClientUpload, ids=ids, client_id=client_id, session=session)


@base.session_aware()
async def list_client_uploads(
    limit: int = 0,
    page: int = 0,
    order: str = 'id',
    desc: bool = False,
    q: str = None,
    applied: bool = False,
    session=None
):
    count = None
    offset = None
    if limit and page:
        count = await get_client_upload_count(q=q)
        limit, offset = utils.get_limit_offset(limit, page)

    query = select(models.ClientUpload)
    if q:
        query = query.where(models.ClientUpload.status.ilike(f'%{q}%').__or__(
            models.ClientUpload.error_message.ilike(f'%{q}%')
        ).__or__(
            models.ClientUpload.message.ilike(f'%{q}%')
        ).__or__(
            models.ClientUpload.output.ilike(f'%{q}%')
        ))
    if applied:
        query = query.where(models.ClientUpload.status == models.ClientUploadStatus.APPLIED)
    else:
        query = query.where(models.ClientUpload.status != models.ClientUploadStatus.APPLIED)

    if limit and offset is not None:
        query = query.limit(limit).offset(offset)

    if not hasattr(models.ClientUpload, order):
        # Set default order
        order = 'id'
    order_col = getattr(models.ClientUpload, order)

    if desc:
        order_col = order_col.desc()
    query = query.order_by(order_col)
    res = await session.execute(query)

    if count is not None:
        return res.scalars().fetchall(), count
    else:
        return res.scalars().fetchall()


@base.session_aware()
async def list_client_uploads_all(
    ids: list[int] = None,
    session=None
):
    query = select(models.ClientUpload)
    if ids:
        query = query.where(models.ClientUpload.id.in_(ids))

    res = await session.execute(query)

    return res.scalars().fetchall()


@base.session_aware()
async def get_client_upload_by_id(id: int, session=None):
    return await base.get_by_id(models.ClientUpload, id, session=session)


@base.session_aware()
async def get_client_upload_count(
    q: str = None,
    session=None,
):
    where = []
    params = {}
    if q:
        q = f'%{q}%'
        where.append('status ILIKE :q OR error_message ILIKE :q OR message ILIKE :q OR output ILIKE :q')
        params['q'] = q

    raw = 'SELECT count(*) AS count FROM client_uploads'
    if where:
        raw = f"{raw} WHERE {' AND '.join(where)}"

    sql = text(raw)

    res = await session.execute(sql, params)
    count = res.scalar()

    return count
