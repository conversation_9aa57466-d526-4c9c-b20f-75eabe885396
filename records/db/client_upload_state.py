"""Atomic state transitions for client uploads with optimistic locking."""

import logging
from typing import Optional, Dict, Any, Set, Tuple
from sqlalchemy import update, select
from sqlalchemy.exc import NoResultFound

from records.db import base, models
from records.db.exceptions import (
    OptimisticLockError,
    InvalidStateTransitionError,
    UploadNotFoundError,
    UploadNotInExpectedStateError
)

logger = logging.getLogger(__name__)

# Valid state transitions mapping
VALID_TRANSITIONS: Dict[str, Set[str]] = {
    models.ClientUploadStatus.PROCESSING: {
        models.ClientUploadStatus.REVIEW,
        models.ClientUploadStatus.ERROR,
        models.ClientUploadStatus.MERGING
    },
    models.ClientUploadStatus.MERGING: {
        models.ClientUploadStatus.REVIEW,
        models.ClientUploadStatus.ERROR
    },
    models.ClientUploadStatus.REVIEW: {
        models.ClientUploadStatus.APPLIED,
        models.ClientUploadStatus.ERROR,
        models.ClientUploadStatus.PROCESSING  # Allow reprocessing
    },
    models.ClientUploadStatus.ERROR: {
        models.ClientUploadStatus.PROCESSING,  # Allow retry
        models.ClientUploadStatus.APPLIED      # Allow manual resolution
    },
    models.ClientUploadStatus.APPLIED: {
        # Terminal state - no transitions allowed
    }
}


def is_valid_transition(from_status: str, to_status: str) -> bool:
    """Check if a state transition is valid."""
    if from_status == to_status:
        return True  # Same state is always valid
    
    return to_status in VALID_TRANSITIONS.get(from_status, set())


def get_timestamp_field_for_status(status: str) -> Optional[str]:
    """Get the timestamp field to update for a given status."""
    timestamp_mapping = {
        models.ClientUploadStatus.PROCESSING: 'processing_started_at',
        models.ClientUploadStatus.REVIEW: 'processing_completed_at',
        models.ClientUploadStatus.ERROR: 'processing_completed_at',
        models.ClientUploadStatus.MERGING: 'merging_started_at',
        models.ClientUploadStatus.APPLIED: 'merging_completed_at'
    }
    return timestamp_mapping.get(status)


@base.session_aware()
async def transition_upload_status(
    upload_id: int,
    to_status: str,
    update_data: Optional[Dict[str, Any]] = None,
    from_status: Optional[str] = None,
    session=None
) -> models.ClientUpload:
    """
    Atomically transition upload status with optimistic locking.
    
    Args:
        upload_id: ID of the upload to transition
        to_status: Target status to transition to
        update_data: Additional data to update (optional)
        from_status: Expected current status (optional, for validation)
        session: Database session
        
    Returns:
        Updated ClientUpload instance
        
    Raises:
        UploadNotFoundError: If upload doesn't exist
        OptimisticLockError: If concurrent modification detected
        InvalidStateTransitionError: If transition is not allowed
        UploadNotInExpectedStateError: If upload not in expected state
    """
    update_data = update_data or {}
    
    # First, get current upload state
    try:
        current_upload = await base.get_by_id(models.ClientUpload, upload_id, session=session)
    except NoResultFound:
        raise UploadNotFoundError(upload_id)
    
    current_status = current_upload.status
    current_version = current_upload.version
    
    # Validate from_status if provided
    if from_status is not None and current_status != from_status:
        raise UploadNotInExpectedStateError(upload_id, from_status, current_status)
    
    # Validate state transition
    if not is_valid_transition(current_status, to_status):
        raise InvalidStateTransitionError(upload_id, current_status, to_status)
    
    # Prepare update data
    update_values = {
        'status': to_status,
        'version': current_version + 1,
        'updated_at': base.now(),
        **update_data
    }
    
    # Add timestamp field if applicable
    timestamp_field = get_timestamp_field_for_status(to_status)
    if timestamp_field:
        update_values[timestamp_field] = base.now()
    
    # Perform atomic update with optimistic locking
    result = await session.execute(
        update(models.ClientUpload)
        .where(
            models.ClientUpload.id == upload_id,
            models.ClientUpload.version == current_version
        )
        .values(update_values)
    )
    
    # Check if update was successful (optimistic lock check)
    if result.rowcount == 0:
        # Re-fetch to get current version for error message
        try:
            updated_upload = await base.get_by_id(models.ClientUpload, upload_id, session=session)
            raise OptimisticLockError(upload_id, current_version, updated_upload.version)
        except NoResultFound:
            raise UploadNotFoundError(upload_id)
    
    # Return updated upload
    updated_upload = await base.get_by_id(models.ClientUpload, upload_id, session=session)
    
    logger.info(
        f"Upload {upload_id} transitioned from '{current_status}' to '{to_status}' "
        f"(version {current_version} -> {updated_upload.version})"
    )
    
    return updated_upload


@base.session_aware()
async def get_upload_with_lock(upload_id: int, session=None) -> models.ClientUpload:
    """
    Get upload with current version for optimistic locking.
    
    Args:
        upload_id: ID of the upload
        session: Database session
        
    Returns:
        ClientUpload instance with current state
        
    Raises:
        UploadNotFoundError: If upload doesn't exist
    """
    try:
        return await base.get_by_id(models.ClientUpload, upload_id, session=session)
    except NoResultFound:
        raise UploadNotFoundError(upload_id)


@base.session_aware()
async def retry_upload_processing(
    upload_id: int,
    clear_error: bool = True,
    session=None
) -> models.ClientUpload:
    """
    Retry processing for a failed upload.
    
    Args:
        upload_id: ID of the upload to retry
        clear_error: Whether to clear the error message
        session: Database session
        
    Returns:
        Updated ClientUpload instance
    """
    update_data = {}
    if clear_error:
        update_data['error_message'] = None
    
    return await transition_upload_status(
        upload_id=upload_id,
        to_status=models.ClientUploadStatus.PROCESSING,
        update_data=update_data,
        from_status=models.ClientUploadStatus.ERROR,
        session=session
    )


@base.session_aware()
async def mark_upload_error(
    upload_id: int,
    error_message: str,
    from_status: Optional[str] = None,
    session=None
) -> models.ClientUpload:
    """
    Mark upload as error with message.
    
    Args:
        upload_id: ID of the upload
        error_message: Error message to set
        from_status: Expected current status (optional)
        session: Database session
        
    Returns:
        Updated ClientUpload instance
    """
    return await transition_upload_status(
        upload_id=upload_id,
        to_status=models.ClientUploadStatus.ERROR,
        update_data={'error_message': error_message},
        from_status=from_status,
        session=session
    )


@base.session_aware()
async def complete_upload_processing(
    upload_id: int,
    output_data: Dict[str, Any],
    session=None
) -> models.ClientUpload:
    """
    Complete initial processing and move to review.
    
    Args:
        upload_id: ID of the upload
        output_data: Processed output data
        session: Database session
        
    Returns:
        Updated ClientUpload instance
    """
    return await transition_upload_status(
        upload_id=upload_id,
        to_status=models.ClientUploadStatus.REVIEW,
        update_data={'output': output_data},
        from_status=models.ClientUploadStatus.PROCESSING,
        session=session
    )


@base.session_aware()
async def start_upload_merging(
    upload_id: int,
    smart_merge_detection_item_id: int,
    session=None
) -> models.ClientUpload:
    """
    Start smart merge process.
    
    Args:
        upload_id: ID of the upload
        smart_merge_detection_item_id: ID of the merge detection item
        session: Database session
        
    Returns:
        Updated ClientUpload instance
    """
    return await transition_upload_status(
        upload_id=upload_id,
        to_status=models.ClientUploadStatus.MERGING,
        update_data={'smart_merge_detection_item_id': smart_merge_detection_item_id},
        from_status=models.ClientUploadStatus.REVIEW,
        session=session
    )


@base.session_aware()
async def complete_upload_merging(
    upload_id: int,
    client_output: Dict[str, Any],
    client_data: Optional[Dict[str, Any]] = None,
    session=None
) -> models.ClientUpload:
    """
    Complete merge process and move to review.
    
    Args:
        upload_id: ID of the upload
        client_output: Merged client output data
        client_data: Client data used in merge (optional)
        session: Database session
        
    Returns:
        Updated ClientUpload instance
    """
    update_data = {'client_output': client_output}
    if client_data is not None:
        update_data['client'] = client_data
    
    return await transition_upload_status(
        upload_id=upload_id,
        to_status=models.ClientUploadStatus.REVIEW,
        update_data=update_data,
        from_status=models.ClientUploadStatus.MERGING,
        session=session
    )


@base.session_aware()
async def apply_upload(upload_id: int, session=None) -> models.ClientUpload:
    """
    Apply upload (mark as completed).
    
    Args:
        upload_id: ID of the upload
        session: Database session
        
    Returns:
        Updated ClientUpload instance
    """
    return await transition_upload_status(
        upload_id=upload_id,
        to_status=models.ClientUploadStatus.APPLIED,
        from_status=models.ClientUploadStatus.REVIEW,
        session=session
    )
