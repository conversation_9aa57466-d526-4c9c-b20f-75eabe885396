"""
Simplified client upload service that centralizes all upload-related logic.
This service handles the complete lifecycle of client uploads.
"""

import io
import json
import logging
from typing import Dict, List, Optional, Tuple

from records.config.shared_config import SharedConfig
from records.db import api as db_api, models
from records.services import detections
from records.utils import json_utils, utils

logger = logging.getLogger(__name__)


class ClientUploadService:
    """Centralized service for managing client upload lifecycle."""
    
    def __init__(self):
        self.ira_client = SharedConfig().ira_client
    
    async def create_upload(self, file_db: models.File, file_data: bytes, manager_id: str) -> models.ClientUpload:
        """Create a new client upload and start initial processing."""
        upload_dict = {
            'file_id': file_db.id,
            'manager_id': manager_id,
            'status': models.ClientUploadStatus.PROCESSING,
        }
        
        upload_db = await db_api.create_client_upload(upload_dict)
        
        # Start initial detection processing
        await self._start_initial_processing(upload_db, file_db, file_data)
        
        return upload_db
    
    async def _start_initial_processing(self, upload_db: models.ClientUpload, file_db: models.File, file_data: bytes):
        """Start initial file processing with IRA detection."""
        detection_ids = await detections.ensure_client_init_detections()
        
        detection_item = await self.ira_client.adetection_item_create(
            detection_ids['client_init_detection'],
            fp=file_data,
            file_name=file_db.name,
            title=file_db.name,
            description=file_db.description,
        )
        
        await db_api.update_client_upload(upload_db, {
            'client_init_detection_id': int(detection_ids['client_init_detection']),
            'smart_merge_detection_id': int(detection_ids['smart_merge_detection']),
            'client_init_detection_item_id': int(detection_item['id']),
        })
    
    async def get_upload_with_status(self, upload_id: int) -> Dict:
        """Get upload with current processing status."""
        upload_db = await db_api.get_client_upload_by_id(upload_id)
        upload_dict = upload_db.to_dict()
        
        # Update status based on detection item
        await self._update_upload_status(upload_dict)
        
        return upload_dict
    
    async def list_uploads_with_status(self, **filters) -> Tuple[List[Dict], int]:
        """List uploads with current processing status."""
        uploads, count = await db_api.list_client_uploads(**filters)
        upload_dicts = [upload.to_dict() for upload in uploads]
        
        # Batch update statuses
        await self._batch_update_upload_statuses(upload_dicts)
        
        return upload_dicts, count
    
    async def _update_upload_status(self, upload_dict: Dict):
        """Update single upload status based on detection item."""
        if upload_dict['status'] == models.ClientUploadStatus.APPLIED:
            return
        
        detection_item = await self._get_detection_item_safe(
            upload_dict['client_init_detection_id'],
            upload_dict['client_init_detection_item_id']
        )
        
        await self._process_detection_result(upload_dict, detection_item)
    
    async def _batch_update_upload_statuses(self, upload_dicts: List[Dict]):
        """Efficiently update multiple upload statuses."""
        if not upload_dicts:
            return
        
        # Get detection items in batch (assuming same detection_id for efficiency)
        first_upload = upload_dicts[0]
        if first_upload.get('client_init_detection_id'):
            detection_items = await detections.list_detection_items(
                first_upload['client_init_detection_id']
            )
            detection_item_map = {item['id']: item for item in detection_items}
        else:
            detection_item_map = {}
        
        # Update each upload
        for upload_dict in upload_dicts:
            if upload_dict['status'] == models.ClientUploadStatus.APPLIED:
                continue
            
            detection_item = detection_item_map.get(
                upload_dict['client_init_detection_item_id'], {}
            )
            await self._process_detection_result(upload_dict, detection_item)
    
    async def _process_detection_result(self, upload_dict: Dict, detection_item: Dict):
        """Process detection item result and update upload accordingly."""
        detection_status = detection_item.get('status', 'PROCESSING')
        upload_id = upload_dict['id']
        
        if detection_status == 'SUCCESS':
            if not upload_dict.get('output'):
                # Extract and save output
                output = self._extract_detection_output(detection_item)
                upload_dict['output'] = output
                
                await db_api.update_client_upload(
                    models.ClientUpload(id=upload_id),
                    {'output': output, 'status': models.ClientUploadStatus.REVIEW}
                )
            upload_dict['status'] = models.ClientUploadStatus.REVIEW
            
        elif detection_status == 'ERROR':
            if not upload_dict.get('error_message'):
                # Extract and save error
                error_message = self._extract_detection_error(detection_item)
                upload_dict['error_message'] = error_message
                
                await db_api.update_client_upload(
                    models.ClientUpload(id=upload_id),
                    {'error_message': error_message, 'status': models.ClientUploadStatus.ERROR}
                )
            upload_dict['status'] = models.ClientUploadStatus.ERROR
            
        else:
            upload_dict['status'] = detection_status
    
    async def merge_uploads_with_clients(self, upload_dicts: List[Dict]) -> List[Dict]:
        """Merge uploads with existing client data."""
        # Find existing clients by EIN
        client_map = await self._find_clients_by_eins(upload_dicts)
        
        # Process each upload
        for upload_dict in upload_dicts:
            await self._process_upload_merge(upload_dict, client_map)
        
        return upload_dicts
    
    async def _find_clients_by_eins(self, upload_dicts: List[Dict]) -> Dict[str, Dict]:
        """Find existing clients by EIN from upload outputs."""
        eins = []
        for upload in upload_dicts:
            output = upload.get('output', {})
            ein = utils.clean_ein(output.get('ein'))
            if ein:
                eins.append(ein)

        if not eins:
            return {}

        # Get clients by EINs - using a simple query for now
        # This would need to be implemented in db_api if not available
        try:
            clients = await db_api.list_clients_by_eins(eins)
            client_map = {}

            for client in clients:
                client_data = client.to_dict()
                ein = utils.clean_ein(client_data.get('ein'))
                if ein:
                    client_map[ein] = client_data

            return client_map
        except AttributeError:
            # Fallback if list_clients_by_eins doesn't exist
            logger.warning("list_clients_by_eins not implemented, returning empty client map")
            return {}
    
    async def _process_upload_merge(self, upload_dict: Dict, client_map: Dict[str, Dict]):
        """Process merge for a single upload."""
        output = upload_dict.get('output', {})
        ein = utils.clean_ein(output.get('ein'))
        
        if not ein:
            upload_dict['client'] = None
            upload_dict['client_output'] = None
            upload_dict['exists'] = False
            return
        
        client_data = client_map.get(ein)
        upload_dict['client'] = client_data
        upload_dict['exists'] = bool(client_data)
        
        if client_data:
            await self._handle_client_merge(upload_dict, client_data)
        else:
            upload_dict['client_output'] = None
    
    async def _handle_client_merge(self, upload_dict: Dict, client_data: Dict):
        """Handle merging with existing client data."""
        merge_detection_item_id = upload_dict.get('smart_merge_detection_item_id')
        
        if merge_detection_item_id:
            # Check existing merge status
            await self._check_merge_status(upload_dict, client_data)
        else:
            # Start new merge process
            await self._start_merge_process(upload_dict, client_data)
        
        # Always provide a merged output for preview
        upload_dict['client_output'] = await json_utils.amerge_jsons([
            client_data, upload_dict.get('output', {})
        ])
    
    async def _check_merge_status(self, upload_dict: Dict, client_data: Dict):
        """Check status of existing merge process."""
        merge_detection_id = upload_dict['smart_merge_detection_id']
        merge_detection_item_id = upload_dict['smart_merge_detection_item_id']
        
        merge_item = await self._get_detection_item_safe(
            merge_detection_id, merge_detection_item_id
        )
        
        merge_status = merge_item.get('status', 'PROCESSING')
        
        if merge_status == 'SUCCESS':
            # Extract merged result
            merged_output = self._extract_detection_output(merge_item)
            upload_dict['client_output'] = merged_output
            upload_dict['status'] = models.ClientUploadStatus.REVIEW
            
            await db_api.update_client_upload(
                models.ClientUpload(id=upload_dict['id']),
                {'client_output': merged_output, 'client': client_data}
            )
            
        elif merge_status == 'ERROR':
            error_message = self._extract_detection_error(merge_item)
            upload_dict['status'] = models.ClientUploadStatus.ERROR
            upload_dict['error_message'] = error_message
            
        else:
            upload_dict['status'] = models.ClientUploadStatus.MERGING
    
    async def _start_merge_process(self, upload_dict: Dict, client_data: Dict):
        """Start new merge process with IRA."""
        merge_detection_id = upload_dict['smart_merge_detection_id']
        
        input_json = {
            'left': client_data,
            'right': upload_dict.get('output', {})
        }
        
        fp = io.BytesIO(json.dumps(input_json).encode())
        merge_item = await self.ira_client.adetection_item_create(
            merge_detection_id,
            fp=fp,
            file_name=f'merge_{client_data.get("name", "unknown")}.json',
            title=f'Merge {client_data.get("name", "unknown")}',
        )
        
        await db_api.update_client_upload(
            models.ClientUpload(id=upload_dict['id']),
            {'smart_merge_detection_item_id': merge_item['id']}
        )
        
        upload_dict['smart_merge_detection_item_id'] = merge_item['id']
        upload_dict['status'] = models.ClientUploadStatus.MERGING
    
    async def _get_detection_item_safe(self, detection_id: int, item_id: int) -> Dict:
        """Safely get detection item, returning empty dict on error."""
        try:
            if detection_id and item_id:
                return await detections.get_detection_item(detection_id, item_id)
        except Exception as e:
            logger.warning(f"Failed to get detection item {item_id}: {e}")
        return {}
    
    def _extract_detection_output(self, detection_item: Dict) -> Dict:
        """Extract output from detection item results."""
        results = detection_item.get('results', [{'output': '{}'}])
        if results:
            output_text = results[-1].get('output', '{}')
            return json_utils.extract_json(output_text)
        return {}
    
    def _extract_detection_error(self, detection_item: Dict) -> str:
        """Extract error message from detection item results."""
        results = detection_item.get('results', [{'output': 'Unknown error'}])
        if results:
            return results[-1].get('output', 'Unknown error')
        return 'Unknown error'


# Global service instance
client_upload_service = ClientUploadService()
