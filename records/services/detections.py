import json
import os
from typing import List

from records.config.shared_config import SharedConfig
from records.db import api as db_api
from records.db import models
from records.services import schemas
from records.utils import utils

EXTRACTOR_PROMPT = """
Rules:
- If a phone is not available or not known or supplied as "0", leave it null.
- status in root should be null
- address_type is one of: ["Mailing Address", "Foreign Address", "Tax return Address", "Legal Address", "Physical Address"]
- business_model is one of: ["Mobile App", "SaaS", "E-Commerce", "S/W outsourcing", "Custom S/W development", "Hardware", "Other"]
- legal_ent_type is one of: ["LLC", "S-CORP", "C-CORP", "SMLLC", "LP"]
- position (contact / shareholder) is one of: ["Main contact", "Contact person", "Corp. Officer", "Director", "Foreign Shareholder", "Manager", "Member", "Secretary", "Shareholder", "CEO", "Other officer"]
 - enumerate all mentioned directors/officers as contacts
- financial year end is one of: ["Jan.", "Feb.", "Mar.", "Apr.", "May.", "June.", "July.", "Aug.", "Sept.", "Oct.", "Nov.", "Dec."]
- share percentage is a number from 0 to 100
- shares.type is one of ["Common", "Preferred"], "Common" if not specified
- state should be correct abbreviation of the state if USA, example "DE" for Delaware, "WY" for Wyoming etc.

Work with addresses:
- Compile the full_address in form [street], [city], [state] [zip], [country]. For example: "365 Main St, Anytown CA 90210, USA"
- For addresses, no extra spaces and punctuation, Capitalize first letter of each word in street names and cities"""


COLD_START_PROMPT = """
Extract company's (client) data according to the suppliant schema.

The output should be formatted as a JSON instance that conforms to the JSON schema below.
If a value can not be determined, then put there null.
Do not fill in the placeholders where specific data is not available.

EXTRACTOR_PROMPT_PLACEHOLDER

As an example, for the schema 
{
  "properties": {
    "foo": {
      "title": "Foo",
      "description": "a list of strings",
      "type": "array",
      "items": {
        "type": "string"
      }
    },
    "bar": {
      "title": "Bar",
      "description": "a string",
      "type": ["string", "null"]
    }
  },
  "required": [
    "foo"
  ]
}
the object 
{"foo": ["ber", "baz"], "bar": null} 
is a well-formatted instance of the schema. The object 
{"properties": {"foo": ["bar", "baz"], "bar": 123}} 
is not well-formatted.

Here is the output schema:
```
GLOBAL_CLIENT_SCHEMA_PLACEHOLDER
```"""


async def list_detection_items(detection_id: str):
    ira_client = SharedConfig().ira_client
    det_items = await ira_client.adetection_item_list(detection_id, limit=5000, page=1)
    return det_items['items']


async def get_detection_item(detection_id: str, item_id: str):
    ira_client = SharedConfig().ira_client
    return await ira_client.adetection_item_get(detection_id, item_id)


async def delete_detection_item(detection_id: str, item_id: str):
    ira_client = SharedConfig().ira_client
    try:
        await ira_client.adetection_item_delete(detection_id, item_id)
    except ValueError as e:
        if 'not found' in str(e).lower():
            pass
        else:
            raise e


async def ensure_client_init_detections():
    ira_client = SharedConfig().ira_client
    # Client init detection
    detection_client_init_db = await db_api.get_external_id_by(
        internal_type=models.DetectionType.CLIENT_DATA,
        external_app_type='detection',
        notfoundok=True
    )

    if not detection_client_init_db:
        detection_ira = await ira_client.adetection_create(
            title=models.DetectionName.CLIENT_DATA,
            type='prompt',
            description='Detection for client data initialization',
            config=get_client_upload_detection_config()
        )
        detection_client_init_db = await db_api.create_external_id({
            'internal_type': models.DetectionType.CLIENT_DATA,
            'external_app_type': 'detection',
            'external_app_id': str(detection_ira['id']),
        })

    # Smart merge detection
    detection_merge_db = await db_api.get_external_id_by(
        internal_type=models.DetectionType.SMART_MERGE,
        external_app_type='detection',
        notfoundok=True
    )
    if not detection_merge_db:
        detection_ira = await ira_client.adetection_create(
            title=models.DetectionName.SMART_MERGE,
            type='smart_merge',
            description='Detection for client data smart merge',
            config=get_client_upload_detection_config()
        )
        detection_merge_db = await db_api.create_external_id({
            'internal_type': models.DetectionType.SMART_MERGE,
            'external_app_type': 'detection',
            'external_app_id': str(detection_ira['id']),
        })

    return {
        'client_init_detection': detection_client_init_db.external_app_id,
        'smart_merge_detection': detection_merge_db.external_app_id
    }


async def delete_client_upload(upload_id: int = None, upload_db: models.ClientUpload = None, delete_file_db: bool = True):
    if not upload_id and not upload_db:
        raise ValueError('Either upload_id or upload_db must be provided')
    if not upload_db:
        upload_db = await db_api.get_client_upload_by_id(upload_id)

    file_db = await db_api.get_client_file_by_id(upload_db.file_id)
    file_manager = SharedConfig().file_manager
    file_path = get_upload_file_path(file_db)
    # Delete detection_item
    await delete_detection_item(upload_db.detection_id, upload_db.detection_item_id)

    # Delete physical file
    await file_manager.delete_file(file_path)

    await db_api.delete_client_upload(upload_db.id)
    if upload_db.file_id and delete_file_db:
        await db_api.delete_client_file(upload_db.file_id)


def get_upload_file_path(file_db: models.File):
    name, ext = os.path.splitext(file_db.name)
    return f'client_uploads/{file_db.name}/{file_db.id}{ext}'


def get_client_upload_detection_config():
    return {
        'prompts': {
            'system_prompt': get_prompt()
        }
    }


def get_client_smart_merge_detection_config():
    return {
        'prompts': {
            'system_prompt': None
        },
        "model_name": "gpt-4o-mini",
        "vision_model": "gpt-4o-mini",
        "temperature": 0.5,
    }


def get_prompt():
    # TODO provide catalogs (address_type etc) and fix placeholders for them
    return COLD_START_PROMPT.replace(
        'GLOBAL_CLIENT_SCHEMA_PLACEHOLDER', json.dumps(schemas.global_client_schema, indent=2)
    ).replace(
        'EXTRACTOR_PROMPT_PLACEHOLDER', EXTRACTOR_PROMPT
    )
